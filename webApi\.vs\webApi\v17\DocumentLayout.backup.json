{"Version": 1, "WorkspaceRootPath": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\services\\hr\\hrservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\services\\hr\\hrservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\services\\finance\\expenseservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\services\\finance\\expenseservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\services\\finance\\accountingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\services\\finance\\accountingservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\services\\finance\\assetmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\services\\finance\\assetmanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-************}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\models\\finance\\asset.cs||{A6C744A8-0E4A-4FC6-886A-************}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\models\\finance\\asset.cs||{A6C744A8-0E4A-4FC6-886A-************}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "HRService.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Services\\HR\\HRService.cs", "RelativeDocumentMoniker": "webApi\\Services\\HR\\HRService.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Services\\HR\\HRService.cs", "RelativeToolTip": "webApi\\Services\\HR\\HRService.cs", "ViewState": "AgIAAOIBAAAAAAAAAAAowP0BAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T20:58:31.424Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AccountingService.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Services\\Finance\\AccountingService.cs", "RelativeDocumentMoniker": "webApi\\Services\\Finance\\AccountingService.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Services\\Finance\\AccountingService.cs", "RelativeToolTip": "webApi\\Services\\Finance\\AccountingService.cs", "ViewState": "AgIAAPEAAAAAAAAAAAAowAMBAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T20:57:55.941Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ExpenseService.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Services\\Finance\\ExpenseService.cs", "RelativeDocumentMoniker": "webApi\\Services\\Finance\\ExpenseService.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Services\\Finance\\ExpenseService.cs", "RelativeToolTip": "webApi\\Services\\Finance\\ExpenseService.cs", "ViewState": "AgIAALACAAAAAAAAAAAowMsCAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T20:56:52.396Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AssetManagementService.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Services\\Finance\\AssetManagementService.cs", "RelativeDocumentMoniker": "webApi\\Services\\Finance\\AssetManagementService.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Services\\Finance\\AssetManagementService.cs", "RelativeToolTip": "webApi\\Services\\Finance\\AssetManagementService.cs", "ViewState": "AgIAANsCAAAAAAAAAAAowPMCAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T20:54:01.585Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "Asset.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Models\\Finance\\Asset.cs", "RelativeDocumentMoniker": "webApi\\Models\\Finance\\Asset.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Models\\Finance\\Asset.cs", "RelativeToolTip": "webApi\\Models\\Finance\\Asset.cs", "ViewState": "AgIAAJgAAAAAAAAAAAAnwKsAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-10T20:50:44.241Z", "EditorCaption": ""}]}]}]}