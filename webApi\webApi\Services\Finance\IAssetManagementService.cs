using webApi.Models.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// واجهة خدمة إدارة الأصول
    /// Asset Management Service Interface
    /// </summary>
    public interface IAssetManagementService
    {
        // ===================================================================
        // إدارة الأصول
        // ===================================================================

        /// <summary>
        /// إنشاء أصل جديد
        /// </summary>
        Task<Asset> CreateAssetAsync(CreateAssetRequest request);

        /// <summary>
        /// الحصول على جميع الأصول
        /// </summary>
        Task<List<Asset>> GetAllAssetsAsync(AssetFilter? filter = null);

        /// <summary>
        /// الحصول على أصل بالمعرف
        /// </summary>
        Task<Asset?> GetAssetByIdAsync(int assetId);

        /// <summary>
        /// تحديث أصل
        /// </summary>
        Task<Asset> UpdateAssetAsync(int assetId, UpdateAssetRequest request);

        /// <summary>
        /// حذف أصل
        /// </summary>
        Task<bool> DeleteAssetAsync(int assetId);

        /// <summary>
        /// تغيير حالة الأصل
        /// </summary>
        Task<bool> ChangeAssetStatusAsync(int assetId, string newStatus, string? reason = null);

        // ===================================================================
        // الاستهلاك
        // ===================================================================

        /// <summary>
        /// حساب استهلاك الأصل
        /// </summary>
        Task<decimal> CalculateDepreciationAsync(int assetId, DateTime asOfDate);

        /// <summary>
        /// تسجيل استهلاك الأصل
        /// </summary>
        Task<AssetDepreciation> RecordDepreciationAsync(CreateDepreciationRequest request);

        /// <summary>
        /// الحصول على سجلات الاستهلاك
        /// </summary>
        Task<List<AssetDepreciation>> GetDepreciationHistoryAsync(int assetId);

        /// <summary>
        /// تشغيل الاستهلاك الدوري
        /// </summary>
        Task<List<AssetDepreciation>> RunPeriodicDepreciationAsync(DateTime periodDate);

        // ===================================================================
        // الصيانة
        // ===================================================================

        /// <summary>
        /// إنشاء سجل صيانة
        /// </summary>
        Task<AssetMaintenance> CreateMaintenanceAsync(CreateMaintenanceRequest request);

        /// <summary>
        /// الحصول على سجلات الصيانة
        /// </summary>
        Task<List<AssetMaintenance>> GetMaintenanceHistoryAsync(int assetId);

        /// <summary>
        /// تحديث حالة الصيانة
        /// </summary>
        Task<bool> UpdateMaintenanceStatusAsync(int maintenanceId, string newStatus);

        /// <summary>
        /// الحصول على الصيانة المجدولة
        /// </summary>
        Task<List<AssetMaintenance>> GetScheduledMaintenanceAsync(DateTime? fromDate = null, DateTime? toDate = null);

        // ===================================================================
        // التقييم
        // ===================================================================

        /// <summary>
        /// إنشاء تقييم للأصل
        /// </summary>
        Task<AssetValuation> CreateValuationAsync(CreateValuationRequest request);

        /// <summary>
        /// الحصول على تاريخ التقييمات
        /// </summary>
        Task<List<AssetValuation>> GetValuationHistoryAsync(int assetId);

        /// <summary>
        /// الحصول على آخر تقييم
        /// </summary>
        Task<AssetValuation?> GetLatestValuationAsync(int assetId);

        // ===================================================================
        // النقل
        // ===================================================================

        /// <summary>
        /// نقل أصل
        /// </summary>
        Task<AssetTransfer> TransferAssetAsync(CreateTransferRequest request);

        /// <summary>
        /// الحصول على تاريخ النقل
        /// </summary>
        Task<List<AssetTransfer>> GetTransferHistoryAsync(int assetId);

        /// <summary>
        /// اعتماد نقل الأصل
        /// </summary>
        Task<bool> ApproveTransferAsync(int transferId, int approvedBy);

        // ===================================================================
        // التقارير
        // ===================================================================

        /// <summary>
        /// تقرير الأصول حسب الفئة
        /// </summary>
        Task<List<AssetCategoryReport>> GetAssetsByCategoryReportAsync();

        /// <summary>
        /// تقرير الاستهلاك
        /// </summary>
        Task<List<DepreciationReport>> GetDepreciationReportAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// تقرير الصيانة
        /// </summary>
        Task<List<MaintenanceReport>> GetMaintenanceReportAsync(DateTime fromDate, DateTime toDate);

        /// <summary>
        /// تقرير الأصول منتهية الصلاحية
        /// </summary>
        Task<List<Asset>> GetExpiringAssetsAsync(int daysAhead = 30);
    }

    // ===================================================================
    // نماذج الطلبات
    // ===================================================================

    public class CreateAssetRequest
    {
        public string AssetCode { get; set; } = string.Empty;
        public string AssetName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string AssetType { get; set; } = string.Empty;
        public string AssetCategory { get; set; } = string.Empty;
        public string? AssetSubcategory { get; set; }
        public int AccountId { get; set; }
        public int? AccumulatedDepreciationAccountId { get; set; }
        public int? DepreciationExpenseAccountId { get; set; }
        public DateTime PurchaseDate { get; set; }
        public decimal PurchaseCost { get; set; }
        public decimal? SalvageValue { get; set; }
        public int? UsefulLifeYears { get; set; }
        public int? UsefulLifeUnits { get; set; }
        public string? DepreciationMethod { get; set; }
        public decimal? DepreciationRate { get; set; }
        public int? LocationId { get; set; }
        public int? DepartmentId { get; set; }
        public int? ResponsibleUserId { get; set; }
        public int? SupplierId { get; set; }
        public string? SerialNumber { get; set; }
        public string? Model { get; set; }
        public string? Manufacturer { get; set; }
        public DateTime? WarrantyStartDate { get; set; }
        public DateTime? WarrantyEndDate { get; set; }
        public string? InsurancePolicyNumber { get; set; }
        public decimal? InsuranceValue { get; set; }
        public DateTime? InsuranceExpiryDate { get; set; }
        public string? ConditionStatus { get; set; }
        public string? Notes { get; set; }
        public int CreatedBy { get; set; }
    }

    public class UpdateAssetRequest
    {
        public string? AssetName { get; set; }
        public string? Description { get; set; }
        public string? AssetCategory { get; set; }
        public string? AssetSubcategory { get; set; }
        public int? LocationId { get; set; }
        public int? DepartmentId { get; set; }
        public int? ResponsibleUserId { get; set; }
        public string? SerialNumber { get; set; }
        public string? Model { get; set; }
        public string? Manufacturer { get; set; }
        public DateTime? WarrantyStartDate { get; set; }
        public DateTime? WarrantyEndDate { get; set; }
        public string? InsurancePolicyNumber { get; set; }
        public decimal? InsuranceValue { get; set; }
        public DateTime? InsuranceExpiryDate { get; set; }
        public string? ConditionStatus { get; set; }
        public string? Notes { get; set; }
        public int UpdatedBy { get; set; }
    }

    public class CreateDepreciationRequest
    {
        public int AssetId { get; set; }
        public DateTime DepreciationDate { get; set; }
        public decimal? DepreciationAmount { get; set; }
        public string? DepreciationMethod { get; set; }
        public string? CalculationBasis { get; set; }
        public string? Notes { get; set; }
        public int CreatedBy { get; set; }
    }

    public class CreateMaintenanceRequest
    {
        public int AssetId { get; set; }
        public string MaintenanceType { get; set; } = string.Empty;
        public DateTime MaintenanceDate { get; set; }
        public DateTime? ScheduledDate { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal? Cost { get; set; }
        public int? VendorId { get; set; }
        public int? TechnicianId { get; set; }
        public string Priority { get; set; } = "medium";
        public string? PartsUsed { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
        public bool IsWarrantyWork { get; set; }
        public string? Notes { get; set; }
        public int CreatedBy { get; set; }
    }

    public class CreateValuationRequest
    {
        public int AssetId { get; set; }
        public DateTime ValuationDate { get; set; }
        public string ValuationMethod { get; set; } = string.Empty;
        public decimal ValuationAmount { get; set; }
        public string? ValuerName { get; set; }
        public string? ValuerLicense { get; set; }
        public string? ValuationReport { get; set; }
        public string? Reason { get; set; }
        public string? Notes { get; set; }
        public bool IsOfficial { get; set; }
        public int CreatedBy { get; set; }
    }

    public class CreateTransferRequest
    {
        public int AssetId { get; set; }
        public DateTime TransferDate { get; set; }
        public int? FromLocationId { get; set; }
        public int? ToLocationId { get; set; }
        public int? FromDepartmentId { get; set; }
        public int? ToDepartmentId { get; set; }
        public int? FromUserId { get; set; }
        public int? ToUserId { get; set; }
        public string Reason { get; set; } = string.Empty;
        public string? ConditionBefore { get; set; }
        public string? ConditionAfter { get; set; }
        public string? Notes { get; set; }
        public int CreatedBy { get; set; }
    }

    // ===================================================================
    // نماذج الفلاتر
    // ===================================================================

    public class AssetFilter
    {
        public string? AssetType { get; set; }
        public string? AssetCategory { get; set; }
        public string? Status { get; set; }
        public int? LocationId { get; set; }
        public int? DepartmentId { get; set; }
        public int? ResponsibleUserId { get; set; }
        public DateTime? PurchaseDateFrom { get; set; }
        public DateTime? PurchaseDateTo { get; set; }
        public decimal? MinValue { get; set; }
        public decimal? MaxValue { get; set; }
        public string? SearchTerm { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }

    // ===================================================================
    // نماذج التقارير
    // ===================================================================

    public class AssetCategoryReport
    {
        public string Category { get; set; } = string.Empty;
        public int AssetCount { get; set; }
        public decimal TotalValue { get; set; }
        public decimal TotalDepreciation { get; set; }
        public decimal NetBookValue { get; set; }
    }

    public class DepreciationReport
    {
        public int AssetId { get; set; }
        public string AssetCode { get; set; } = string.Empty;
        public string AssetName { get; set; } = string.Empty;
        public decimal PurchaseCost { get; set; }
        public decimal AccumulatedDepreciation { get; set; }
        public decimal BookValue { get; set; }
        public decimal CurrentPeriodDepreciation { get; set; }
    }

    public class MaintenanceReport
    {
        public int AssetId { get; set; }
        public string AssetCode { get; set; } = string.Empty;
        public string AssetName { get; set; } = string.Empty;
        public int MaintenanceCount { get; set; }
        public decimal TotalMaintenanceCost { get; set; }
        public DateTime? LastMaintenanceDate { get; set; }
        public DateTime? NextMaintenanceDate { get; set; }
    }
}
