using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Models.Finance;

namespace webApi.Services.Finance
{
    /// <summary>
    /// خدمة إدارة الأصول
    /// Asset Management Service Implementation
    /// </summary>
    public class AssetManagementService : IAssetManagementService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<AssetManagementService> _logger;

        public AssetManagementService(TasksDbContext context, ILogger<AssetManagementService> logger)
        {
            _context = context;
            _logger = logger;
        }

        // ===================================================================
        // إدارة الأصول
        // ===================================================================

        public async Task<Asset> CreateAssetAsync(CreateAssetRequest request)
        {
            try
            {
                // التحقق من عدم تكرار رمز الأصل
                var existingAsset = await _context.Set<Asset>()
                    .FirstOrDefaultAsync(a => a.AssetCode == request.AssetCode);

                if (existingAsset != null)
                {
                    throw new InvalidOperationException($"رمز الأصل {request.AssetCode} موجود مسبقاً");
                }

                var asset = new Asset
                {
                    AssetCode = request.AssetCode,
                    AssetName = request.AssetName,
                    Description = request.Description,
                    AssetType = request.AssetType,
                    AssetCategory = request.AssetCategory,
                    AssetSubcategory = request.AssetSubcategory,
                    AccountId = request.AccountId,
                    AccumulatedDepreciationAccountId = request.AccumulatedDepreciationAccountId,
                    DepreciationExpenseAccountId = request.DepreciationExpenseAccountId,
                    PurchaseDate = request.PurchaseDate,
                    PurchaseCost = request.PurchaseCost,
                    CurrentValue = request.PurchaseCost,
                    SalvageValue = request.SalvageValue ?? 0,
                    UsefulLifeYears = request.UsefulLifeYears,
                    UsefulLifeUnits = request.UsefulLifeUnits,
                    DepreciationMethod = request.DepreciationMethod,
                    DepreciationRate = request.DepreciationRate,
                    LocationId = request.LocationId,
                    DepartmentId = request.DepartmentId,
                    ResponsibleUserId = request.ResponsibleUserId,
                    SupplierId = request.SupplierId,
                    SerialNumber = request.SerialNumber,
                    Model = request.Model,
                    Manufacturer = request.Manufacturer,
                    WarrantyStartDate = request.WarrantyStartDate,
                    WarrantyEndDate = request.WarrantyEndDate,
                    InsurancePolicyNumber = request.InsurancePolicyNumber,
                    InsuranceValue = request.InsuranceValue,
                    InsuranceExpiryDate = request.InsuranceExpiryDate,
                    ConditionStatus = request.ConditionStatus,
                    Notes = request.Notes,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<Asset>().Add(asset);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء أصل جديد: {AssetCode} - {AssetName}", 
                    asset.AssetCode, asset.AssetName);

                return asset;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء الأصل: {AssetCode}", request.AssetCode);
                throw;
            }
        }

        public async Task<List<Asset>> GetAllAssetsAsync(AssetFilter? filter = null)
        {
            try
            {
                var query = _context.Set<Asset>()
                    .Include(a => a.Account)
                    // .Include(a => a.Location) // Location model not available
                    .Include(a => a.Department)
                    .Include(a => a.ResponsibleUser)
                    .Include(a => a.Creator)
                    .Where(a => a.IsActive);

                if (filter != null)
                {
                    if (!string.IsNullOrEmpty(filter.AssetType))
                        query = query.Where(a => a.AssetType == filter.AssetType);

                    if (!string.IsNullOrEmpty(filter.AssetCategory))
                        query = query.Where(a => a.AssetCategory == filter.AssetCategory);

                    if (!string.IsNullOrEmpty(filter.Status))
                        query = query.Where(a => a.Status == filter.Status);

                    if (filter.LocationId.HasValue)
                        query = query.Where(a => a.LocationId == filter.LocationId.Value);

                    if (filter.DepartmentId.HasValue)
                        query = query.Where(a => a.DepartmentId == filter.DepartmentId.Value);

                    if (filter.ResponsibleUserId.HasValue)
                        query = query.Where(a => a.ResponsibleUserId == filter.ResponsibleUserId.Value);

                    if (filter.PurchaseDateFrom.HasValue)
                        query = query.Where(a => a.PurchaseDate >= filter.PurchaseDateFrom.Value);

                    if (filter.PurchaseDateTo.HasValue)
                        query = query.Where(a => a.PurchaseDate <= filter.PurchaseDateTo.Value);

                    if (filter.MinValue.HasValue)
                        query = query.Where(a => a.CurrentValue >= filter.MinValue.Value);

                    if (filter.MaxValue.HasValue)
                        query = query.Where(a => a.CurrentValue <= filter.MaxValue.Value);

                    if (!string.IsNullOrEmpty(filter.SearchTerm))
                    {
                        var searchTerm = filter.SearchTerm.ToLower();
                        query = query.Where(a => 
                            a.AssetCode.ToLower().Contains(searchTerm) ||
                            a.AssetName.ToLower().Contains(searchTerm) ||
                            (a.SerialNumber != null && a.SerialNumber.ToLower().Contains(searchTerm)));
                    }
                }

                return await query
                    .OrderBy(a => a.AssetCode)
                    .Skip((filter?.PageNumber - 1 ?? 0) * (filter?.PageSize ?? 50))
                    .Take(filter?.PageSize ?? 50)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصول");
                throw;
            }
        }

        public async Task<Asset?> GetAssetByIdAsync(int assetId)
        {
            try
            {
                return await _context.Set<Asset>()
                    .Include(a => a.Account)
                    .Include(a => a.AccumulatedDepreciationAccount)
                    .Include(a => a.DepreciationExpenseAccount)
                    // .Include(a => a.Location) // Location model not available
                    .Include(a => a.Department)
                    .Include(a => a.ResponsibleUser)
                    .Include(a => a.Creator)
                    .Include(a => a.Depreciations)
                    .Include(a => a.MaintenanceRecords)
                    .Include(a => a.Valuations)
                    .Include(a => a.Transfers)
                    .FirstOrDefaultAsync(a => a.Id == assetId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصل: {AssetId}", assetId);
                throw;
            }
        }

        public async Task<Asset> UpdateAssetAsync(int assetId, UpdateAssetRequest request)
        {
            try
            {
                var asset = await _context.Set<Asset>().FindAsync(assetId);
                if (asset == null)
                {
                    throw new ArgumentException($"الأصل غير موجود: {assetId}");
                }

                // تحديث الحقول المطلوبة فقط
                if (!string.IsNullOrEmpty(request.AssetName))
                    asset.AssetName = request.AssetName;

                if (request.Description != null)
                    asset.Description = request.Description;

                if (!string.IsNullOrEmpty(request.AssetCategory))
                    asset.AssetCategory = request.AssetCategory;

                if (request.AssetSubcategory != null)
                    asset.AssetSubcategory = request.AssetSubcategory;

                if (request.LocationId.HasValue)
                    asset.LocationId = request.LocationId;

                if (request.DepartmentId.HasValue)
                    asset.DepartmentId = request.DepartmentId;

                if (request.ResponsibleUserId.HasValue)
                    asset.ResponsibleUserId = request.ResponsibleUserId;

                if (request.SerialNumber != null)
                    asset.SerialNumber = request.SerialNumber;

                if (request.Model != null)
                    asset.Model = request.Model;

                if (request.Manufacturer != null)
                    asset.Manufacturer = request.Manufacturer;

                if (request.WarrantyStartDate.HasValue)
                    asset.WarrantyStartDate = request.WarrantyStartDate;

                if (request.WarrantyEndDate.HasValue)
                    asset.WarrantyEndDate = request.WarrantyEndDate;

                if (request.InsurancePolicyNumber != null)
                    asset.InsurancePolicyNumber = request.InsurancePolicyNumber;

                if (request.InsuranceValue.HasValue)
                    asset.InsuranceValue = request.InsuranceValue;

                if (request.InsuranceExpiryDate.HasValue)
                    asset.InsuranceExpiryDate = request.InsuranceExpiryDate;

                if (request.ConditionStatus != null)
                    asset.ConditionStatus = request.ConditionStatus;

                if (request.Notes != null)
                    asset.Notes = request.Notes;

                asset.UpdatedBy = request.UpdatedBy;
                asset.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث الأصل: {AssetId}", assetId);

                return asset;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث الأصل: {AssetId}", assetId);
                throw;
            }
        }

        public async Task<bool> DeleteAssetAsync(int assetId)
        {
            try
            {
                var asset = await _context.Set<Asset>().FindAsync(assetId);
                if (asset == null)
                {
                    return false;
                }

                asset.IsActive = false;
                asset.Status = "deleted";
                asset.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم حذف الأصل: {AssetId}", assetId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الأصل: {AssetId}", assetId);
                throw;
            }
        }

        public async Task<bool> ChangeAssetStatusAsync(int assetId, string newStatus, string? reason = null)
        {
            try
            {
                var asset = await _context.Set<Asset>().FindAsync(assetId);
                if (asset == null)
                {
                    return false;
                }

                var oldStatus = asset.Status;
                asset.Status = newStatus;
                asset.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                if (newStatus == "disposed" || newStatus == "sold")
                {
                    asset.DisposalDate = DateTime.Now;
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تغيير حالة الأصل {AssetId} من {OldStatus} إلى {NewStatus}. السبب: {Reason}", 
                    assetId, oldStatus, newStatus, reason);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تغيير حالة الأصل: {AssetId}", assetId);
                throw;
            }
        }

        // ===================================================================
        // الاستهلاك
        // ===================================================================

        public async Task<decimal> CalculateDepreciationAsync(int assetId, DateTime asOfDate)
        {
            try
            {
                var asset = await _context.Set<Asset>().FindAsync(assetId);
                if (asset == null || !asset.IsDepreciable)
                {
                    return 0;
                }

                var depreciableAmount = asset.PurchaseCost - asset.SalvageValue;
                var yearsElapsed = (asOfDate - asset.PurchaseDate).TotalDays / 365.25;

                return asset.DepreciationMethod switch
                {
                    "straight_line" => CalculateStraightLineDepreciation(depreciableAmount, asset.UsefulLifeYears!.Value, yearsElapsed),
                    "declining_balance" => CalculateDecliningBalanceDepreciation(asset.PurchaseCost, asset.DepreciationRate ?? 0, yearsElapsed),
                    _ => CalculateStraightLineDepreciation(depreciableAmount, asset.UsefulLifeYears!.Value, yearsElapsed)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب استهلاك الأصل: {AssetId}", assetId);
                throw;
            }
        }

        private static decimal CalculateStraightLineDepreciation(decimal depreciableAmount, int usefulLifeYears, double yearsElapsed)
        {
            var annualDepreciation = depreciableAmount / usefulLifeYears;
            var totalDepreciation = annualDepreciation * (decimal)yearsElapsed;
            return Math.Min(totalDepreciation, depreciableAmount);
        }

        private static decimal CalculateDecliningBalanceDepreciation(decimal purchaseCost, decimal depreciationRate, double yearsElapsed)
        {
            var remainingValue = purchaseCost;
            for (int year = 0; year < (int)yearsElapsed; year++)
            {
                remainingValue *= (1 - depreciationRate / 100);
            }
            return purchaseCost - remainingValue;
        }

        public async Task<AssetDepreciation> RecordDepreciationAsync(CreateDepreciationRequest request)
        {
            try
            {
                var asset = await _context.Set<Asset>().FindAsync(request.AssetId);
                if (asset == null)
                {
                    throw new ArgumentException($"الأصل غير موجود: {request.AssetId}");
                }

                var depreciationAmount = request.DepreciationAmount ?? 
                    await CalculateDepreciationAsync(request.AssetId, request.DepreciationDate);

                var newAccumulatedDepreciation = asset.AccumulatedDepreciation + depreciationAmount;
                var newBookValue = asset.PurchaseCost - newAccumulatedDepreciation;

                var depreciation = new AssetDepreciation
                {
                    AssetId = request.AssetId,
                    DepreciationDate = request.DepreciationDate,
                    DepreciationAmount = depreciationAmount,
                    AccumulatedDepreciation = newAccumulatedDepreciation,
                    BookValue = newBookValue,
                    DepreciationMethod = request.DepreciationMethod ?? asset.DepreciationMethod,
                    CalculationBasis = request.CalculationBasis,
                    Notes = request.Notes,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                // تحديث الأصل
                asset.AccumulatedDepreciation = newAccumulatedDepreciation;
                asset.CurrentValue = newBookValue;
                asset.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                _context.Set<AssetDepreciation>().Add(depreciation);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تسجيل استهلاك للأصل {AssetId} بمبلغ {Amount}", 
                    request.AssetId, depreciationAmount);

                return depreciation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تسجيل استهلاك الأصل: {AssetId}", request.AssetId);
                throw;
            }
        }

        public async Task<List<AssetDepreciation>> GetDepreciationHistoryAsync(int assetId)
        {
            try
            {
                return await _context.Set<AssetDepreciation>()
                    .Include(d => d.Asset)
                    .Include(d => d.Creator)
                    .Where(d => d.AssetId == assetId)
                    .OrderByDescending(d => d.DepreciationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ الاستهلاك: {AssetId}", assetId);
                throw;
            }
        }

        public async Task<List<AssetDepreciation>> RunPeriodicDepreciationAsync(DateTime periodDate)
        {
            try
            {
                var depreciableAssets = await _context.Set<Asset>()
                    .Where(a => a.IsActive && 
                               a.AssetType == "fixed" && 
                               a.UsefulLifeYears.HasValue && 
                               a.UsefulLifeYears > 0 &&
                               a.Status == "active")
                    .ToListAsync();

                var depreciations = new List<AssetDepreciation>();

                foreach (var asset in depreciableAssets)
                {
                    // التحقق من عدم وجود استهلاك لهذه الفترة
                    var existingDepreciation = await _context.Set<AssetDepreciation>()
                        .FirstOrDefaultAsync(d => d.AssetId == asset.Id && 
                                                 d.DepreciationDate.Month == periodDate.Month &&
                                                 d.DepreciationDate.Year == periodDate.Year);

                    if (existingDepreciation == null)
                    {
                        var request = new CreateDepreciationRequest
                        {
                            AssetId = asset.Id,
                            DepreciationDate = periodDate,
                            CreatedBy = 1 // System user
                        };

                        var depreciation = await RecordDepreciationAsync(request);
                        depreciations.Add(depreciation);
                    }
                }

                _logger.LogInformation("تم تشغيل الاستهلاك الدوري لـ {Count} أصل", depreciations.Count);

                return depreciations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تشغيل الاستهلاك الدوري");
                throw;
            }
        }

        // ===================================================================
        // الصيانة
        // ===================================================================

        public async Task<AssetMaintenance> CreateMaintenanceAsync(CreateMaintenanceRequest request)
        {
            try
            {
                var maintenance = new AssetMaintenance
                {
                    AssetId = request.AssetId,
                    MaintenanceType = request.MaintenanceType,
                    MaintenanceDate = request.MaintenanceDate,
                    ScheduledDate = request.ScheduledDate,
                    Description = request.Description,
                    Cost = request.Cost,
                    VendorId = request.VendorId,
                    TechnicianId = request.TechnicianId,
                    Priority = request.Priority,
                    PartsUsed = request.PartsUsed,
                    NextMaintenanceDate = request.NextMaintenanceDate,
                    IsWarrantyWork = request.IsWarrantyWork,
                    Notes = request.Notes,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<AssetMaintenance>().Add(maintenance);
                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء سجل صيانة للأصل: {AssetId}", request.AssetId);

                return maintenance;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء سجل الصيانة: {AssetId}", request.AssetId);
                throw;
            }
        }

        public async Task<List<AssetMaintenance>> GetMaintenanceHistoryAsync(int assetId)
        {
            try
            {
                return await _context.Set<AssetMaintenance>()
                    .Include(m => m.Asset)
                    .Include(m => m.Technician)
                    .Include(m => m.Creator)
                    .Where(m => m.AssetId == assetId)
                    .OrderByDescending(m => m.MaintenanceDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ الصيانة: {AssetId}", assetId);
                throw;
            }
        }

        public async Task<bool> UpdateMaintenanceStatusAsync(int maintenanceId, string newStatus)
        {
            try
            {
                var maintenance = await _context.Set<AssetMaintenance>().FindAsync(maintenanceId);
                if (maintenance == null)
                {
                    return false;
                }

                maintenance.Status = newStatus;
                if (newStatus == "completed")
                {
                    maintenance.CompletedDate = DateTime.Now;
                }
                maintenance.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم تحديث حالة الصيانة {MaintenanceId} إلى {Status}", 
                    maintenanceId, newStatus);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث حالة الصيانة: {MaintenanceId}", maintenanceId);
                throw;
            }
        }

        public async Task<List<AssetMaintenance>> GetScheduledMaintenanceAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.Set<AssetMaintenance>()
                    .Include(m => m.Asset)
                    .Include(m => m.Technician)
                    .Where(m => m.Status == "scheduled");

                if (fromDate.HasValue)
                    query = query.Where(m => m.ScheduledDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(m => m.ScheduledDate <= toDate.Value);

                return await query
                    .OrderBy(m => m.ScheduledDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الصيانة المجدولة");
                throw;
            }
        }

        // ===================================================================
        // التقييم
        // ===================================================================

        public async Task<AssetValuation> CreateValuationAsync(CreateValuationRequest request)
        {
            try
            {
                // الحصول على آخر تقييم
                var previousValuation = await GetLatestValuationAsync(request.AssetId);

                var valuation = new AssetValuation
                {
                    AssetId = request.AssetId,
                    ValuationDate = request.ValuationDate,
                    ValuationMethod = request.ValuationMethod,
                    ValuationAmount = request.ValuationAmount,
                    PreviousValue = previousValuation?.ValuationAmount,
                    ValuerName = request.ValuerName,
                    ValuerLicense = request.ValuerLicense,
                    ValuationReport = request.ValuationReport,
                    Reason = request.Reason,
                    Notes = request.Notes,
                    IsOfficial = request.IsOfficial,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<AssetValuation>().Add(valuation);

                // تحديث القيمة الحالية للأصل إذا كان التقييم رسمياً
                if (request.IsOfficial)
                {
                    var asset = await _context.Set<Asset>().FindAsync(request.AssetId);
                    if (asset != null)
                    {
                        asset.CurrentValue = request.ValuationAmount;
                        asset.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                    }
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم إنشاء تقييم للأصل {AssetId} بقيمة {Amount}", 
                    request.AssetId, request.ValuationAmount);

                return valuation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء تقييم الأصل: {AssetId}", request.AssetId);
                throw;
            }
        }

        public async Task<List<AssetValuation>> GetValuationHistoryAsync(int assetId)
        {
            try
            {
                return await _context.Set<AssetValuation>()
                    .Include(v => v.Asset)
                    .Include(v => v.Creator)
                    .Where(v => v.AssetId == assetId)
                    .OrderByDescending(v => v.ValuationDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ التقييمات: {AssetId}", assetId);
                throw;
            }
        }

        public async Task<AssetValuation?> GetLatestValuationAsync(int assetId)
        {
            try
            {
                return await _context.Set<AssetValuation>()
                    .Where(v => v.AssetId == assetId)
                    .OrderByDescending(v => v.ValuationDate)
                    .FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على آخر تقييم: {AssetId}", assetId);
                throw;
            }
        }

        // ===================================================================
        // النقل
        // ===================================================================

        public async Task<AssetTransfer> TransferAssetAsync(CreateTransferRequest request)
        {
            try
            {
                var transfer = new AssetTransfer
                {
                    AssetId = request.AssetId,
                    TransferDate = request.TransferDate,
                    FromLocationId = request.FromLocationId,
                    ToLocationId = request.ToLocationId,
                    FromDepartmentId = request.FromDepartmentId,
                    ToDepartmentId = request.ToDepartmentId,
                    FromResponsibleUserId = request.FromUserId,
                    ToResponsibleUserId = request.ToUserId,
                    Reason = request.Reason,
                    ConditionBefore = request.ConditionBefore,
                    ConditionAfter = request.ConditionAfter,
                    Notes = request.Notes,
                    CreatedBy = request.CreatedBy,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                _context.Set<AssetTransfer>().Add(transfer);

                // تحديث موقع الأصل
                var asset = await _context.Set<Asset>().FindAsync(request.AssetId);
                if (asset != null)
                {
                    if (request.ToLocationId.HasValue)
                        asset.LocationId = request.ToLocationId;

                    if (request.ToDepartmentId.HasValue)
                        asset.DepartmentId = request.ToDepartmentId;

                    if (request.ToUserId.HasValue)
                        asset.ResponsibleUserId = request.ToUserId;

                    asset.UpdatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم نقل الأصل {AssetId}", request.AssetId);

                return transfer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في نقل الأصل: {AssetId}", request.AssetId);
                throw;
            }
        }

        public async Task<List<AssetTransfer>> GetTransferHistoryAsync(int assetId)
        {
            try
            {
                return await _context.Set<AssetTransfer>()
                    .Include(t => t.Asset)
                    // .Include(t => t.FromLocation) // Location model not available
                    // .Include(t => t.ToLocation) // Location model not available
                    .Include(t => t.FromDepartment)
                    .Include(t => t.ToDepartment)
                    .Include(t => t.FromResponsibleUser)
                    .Include(t => t.ToResponsibleUser)
                    .Include(t => t.Creator)
                    .Where(t => t.AssetId == assetId)
                    .OrderByDescending(t => t.TransferDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على تاريخ النقل: {AssetId}", assetId);
                throw;
            }
        }

        public async Task<bool> ApproveTransferAsync(int transferId, int approvedBy)
        {
            try
            {
                var transfer = await _context.Set<AssetTransfer>().FindAsync(transferId);
                if (transfer == null)
                {
                    return false;
                }

                transfer.ApprovedBy = approvedBy;
                transfer.ApprovedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                await _context.SaveChangesAsync();

                _logger.LogInformation("تم اعتماد نقل الأصل: {TransferId}", transferId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في اعتماد نقل الأصل: {TransferId}", transferId);
                throw;
            }
        }

        // ===================================================================
        // التقارير
        // ===================================================================

        public async Task<List<AssetCategoryReport>> GetAssetsByCategoryReportAsync()
        {
            try
            {
                return await _context.Set<Asset>()
                    .Where(a => a.IsActive)
                    .GroupBy(a => a.AssetCategory)
                    .Select(g => new AssetCategoryReport
                    {
                        Category = g.Key,
                        AssetCount = g.Count(),
                        TotalValue = g.Sum(a => a.CurrentValue),
                        TotalDepreciation = g.Sum(a => a.AccumulatedDepreciation),
                        NetBookValue = g.Sum(a => a.CurrentValue - a.AccumulatedDepreciation)
                    })
                    .OrderBy(r => r.Category)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير الأصول حسب الفئة");
                throw;
            }
        }

        public async Task<List<DepreciationReport>> GetDepreciationReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                return await _context.Set<Asset>()
                    .Where(a => a.IsActive && a.AssetType == "fixed")
                    .Select(a => new DepreciationReport
                    {
                        AssetId = a.Id,
                        AssetCode = a.AssetCode,
                        AssetName = a.AssetName,
                        PurchaseCost = a.PurchaseCost,
                        AccumulatedDepreciation = a.AccumulatedDepreciation,
                        BookValue = a.CurrentValue,
                        CurrentPeriodDepreciation = a.Depreciations
                            .Where(d => d.DepreciationDate >= fromDate && d.DepreciationDate <= toDate)
                            .Sum(d => d.DepreciationAmount)
                    })
                    .OrderBy(r => r.AssetCode)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير الاستهلاك");
                throw;
            }
        }

        public async Task<List<MaintenanceReport>> GetMaintenanceReportAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                return await _context.Set<Asset>()
                    .Where(a => a.IsActive)
                    .Select(a => new MaintenanceReport
                    {
                        AssetId = a.Id,
                        AssetCode = a.AssetCode,
                        AssetName = a.AssetName,
                        MaintenanceCount = a.MaintenanceRecords
                            .Count(m => m.MaintenanceDate >= fromDate && m.MaintenanceDate <= toDate),
                        TotalMaintenanceCost = a.MaintenanceRecords
                            .Where(m => m.MaintenanceDate >= fromDate && m.MaintenanceDate <= toDate)
                            .Sum(m => m.Cost ?? 0),
                        LastMaintenanceDate = a.MaintenanceRecords
                            .Where(m => m.Status == "completed")
                            .OrderByDescending(m => m.CompletedDate)
                            .Select(m => m.CompletedDate)
                            .FirstOrDefault(),
                        NextMaintenanceDate = a.MaintenanceRecords
                            .Where(m => m.Status == "scheduled")
                            .OrderBy(m => m.ScheduledDate)
                            .Select(m => m.ScheduledDate)
                            .FirstOrDefault()
                    })
                    .Where(r => r.MaintenanceCount > 0)
                    .OrderBy(r => r.AssetCode)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنتاج تقرير الصيانة");
                throw;
            }
        }

        public async Task<List<Asset>> GetExpiringAssetsAsync(int daysAhead = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(daysAhead);

                return await _context.Set<Asset>()
                    .Where(a => a.IsActive && 
                               (a.WarrantyEndDate <= cutoffDate || 
                                a.InsuranceExpiryDate <= cutoffDate))
                    // .Include(a => a.Location) // Location model not available
                    .Include(a => a.Department)
                    .Include(a => a.ResponsibleUser)
                    .OrderBy(a => a.WarrantyEndDate ?? a.InsuranceExpiryDate)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على الأصول منتهية الصلاحية");
                throw;
            }
        }
    }
}
